import Mathlib.Analysis.MeanInequalities
import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Order.Field.Basic
import Mathlib.Algebra.Order.BigOperators.Ring.Finset

-- Theorem: For all positive real numbers x, y, z: 9/(x + y + z) ≤ 2/(x + y) + 2/(y + z) + 2/(z + x)
theorem algebra_9onxpypzleqsum2onxpy (x y z : ℝ) (hx : 0 < x) (hy : 0 < y) (hz : 0 < z) :
  9 / (x + y + z) ≤ 2 / (x + y) + 2 / (y + z) + 2 / (z + x) := by
  -- Method 1: Using <PERSON>gel (Titu's) inequality
  -- Step 1: Show that Σ 1/(x + y) ≥ 9/[2(x + y + z)]
  have h1 : 1 / (x + y) + 1 / (y + z) + 1 / (z + x) ≥ 9 / (2 * (x + y + z)) := by
    -- Apply Cau<PERSON>-<PERSON> inequality in the form: (∑ aᵢ)² ≤ (∑ bᵢ²)(∑ cᵢ²) where aᵢ = bᵢcᵢ
    -- Set aᵢ = 1, bᵢ = 1/√gᵢ, cᵢ = √gᵢ where gᵢ are (x+y), (y+z), (z+x)
    have pos_xy : 0 < x + y := add_pos hx hy
    have pos_yz : 0 < y + z := add_pos hy hz
    have pos_zx : 0 < z + x := add_pos hz hx
    -- Use the identity: (1 + 1 + 1)² / ((x+y) + (y+z) + (z+x)) ≤ 1/(x+y) + 1/(y+z) + 1/(z+x)
    -- Since (x+y) + (y+z) + (z+x) = 2(x+y+z), we get 9/(2(x+y+z)) ≤ 1/(x+y) + 1/(y+z) + 1/(z+x)
    have sum_eq : (x + y) + (y + z) + (z + x) = 2 * (x + y + z) := by ring
    -- Apply harmonic-arithmetic mean inequality
    have ham_ineq : 3 / (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≤ ((x + y) + (y + z) + (z + x)) / 3 := by
      -- Apply HM ≤ GM ≤ AM inequality chain
      -- For three positive numbers a, b, c: 3/(1/a + 1/b + 1/c) ≤ (a + b + c)/3
      -- This is the harmonic-arithmetic mean inequality
      have h_pos : 0 < 1 / (x + y) + 1 / (y + z) + 1 / (z + x) := by
        apply add_pos
        · apply add_pos
          · exact one_div_pos.mpr pos_xy
          · exact one_div_pos.mpr pos_yz
        · exact one_div_pos.mpr pos_zx
      -- Use the weighted HM-GM inequality with equal weights
      let s : Finset (Fin 3) := Finset.univ
      let w : Fin 3 → ℝ := fun _ => 1/3
      let z : Fin 3 → ℝ := fun i => if i = 0 then x + y else if i = 1 then y + z else z + x
      have hw_pos : ∀ i ∈ s, 0 < w i := by
        intro i _
        simp [w]
        norm_num
      have hw_sum : ∑ i ∈ s, w i = 1 := by
        simp [s, w]
        norm_num
      have hz_pos : ∀ i ∈ s, 0 < z i := by
        intro i _
        fin_cases i <;> simp [z, pos_xy, pos_yz, pos_zx]
      have hs_nonempty : s.Nonempty := by
        simp [s]
      -- Apply harm_mean_le_geom_mean_weighted
      have hm_gm := Real.harm_mean_le_geom_mean_weighted s w z hs_nonempty hw_pos hw_sum hz_pos
      -- Apply geom_mean_le_arith_mean_weighted
      have gm_am := Real.geom_mean_le_arith_mean_weighted s w z (fun i hi => le_of_lt (hw_pos i hi)) hw_sum (fun i hi => le_of_lt (hz_pos i hi))
      -- Combine HM ≤ GM ≤ AM
      have hm_am := le_trans hm_gm gm_am
      -- Simplify the expressions
      simp [s, w, z] at hm_am
      convert hm_am using 1
      · ring
      · ring
    rw [sum_eq] at ham_ineq
    -- Rearrange to get the desired inequality
    have : 9 / (2 * (x + y + z)) ≤ 1 / (x + y) + 1 / (y + z) + 1 / (z + x) := by
      sorry -- Algebraic manipulation from ham_ineq
    exact this
  -- Step 2: Multiply by 2 to get final inequality
  have h2 : 2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 / (x + y + z) := by
    -- From h1: 1/(x+y) + 1/(y+z) + 1/(z+x) ≥ 9/(2(x+y+z))
    -- Multiply both sides by 2
    have h_mul : 2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 2 * (9 / (2 * (x + y + z))) := by
      exact mul_le_mul_of_nonneg_left h1 (by norm_num)
    -- Simplify: 2 * (9 / (2 * (x + y + z))) = 9 / (x + y + z)
    have h_simp : 2 * (9 / (2 * (x + y + z))) = 9 / (x + y + z) := by
      field_simp
      ring
    rw [h_simp] at h_mul
    exact h_mul
  -- Step 3: Rearrange to get desired form
  -- From h2: 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9/(x+y+z)
  -- We want: 9/(x+y+z) ≤ 2/(x+y) + 2/(y+z) + 2/(z+x)
  -- Note that 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) = 2/(x+y) + 2/(y+z) + 2/(z+x)
  have h_expand : 2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) = 2 / (x + y) + 2 / (y + z) + 2 / (z + x) := by
    ring
  rw [h_expand] at h2
  exact h2
